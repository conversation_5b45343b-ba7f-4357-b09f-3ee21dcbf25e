* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    overflow: hidden;
}

#game-container {
    width: 100vw;
    height: 100vh;
    position: relative;
}

.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
}

.screen.active {
    display: block;
}

/* Login Screen */
#login-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    min-width: 300px;
}

.login-box h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.login-box p {
    margin-bottom: 30px;
    opacity: 0.9;
}

#login-form input {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 16px;
}

#login-form button {
    width: 100%;
    padding: 12px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s;
}

#login-form button:hover {
    background: #45a049;
}

#login-status {
    margin-top: 15px;
    font-size: 14px;
}

/* Game Screen */
#game-screen {
    background: #000;
}

#phaser-game {
    position: absolute;
    top: 0;
    left: 0;
}

#game-ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

.ui-panel {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #444;
    border-radius: 8px;
    padding: 10px;
    pointer-events: auto;
}

/* Player Info Panel */
#player-info {
    top: 10px;
    left: 10px;
    width: 250px;
}

.player-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #4CAF50;
}

.player-level {
    font-size: 14px;
    margin-bottom: 10px;
    color: #FFD700;
}

.health-bar, .mana-bar {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.bar-label {
    width: 30px;
    font-size: 12px;
    font-weight: bold;
}

.bar {
    flex: 1;
    height: 20px;
    background: #333;
    border: 1px solid #666;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 8px;
}

.bar-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.health-fill {
    background: linear-gradient(90deg, #ff4444, #ff6666);
}

.mana-fill {
    background: linear-gradient(90deg, #4444ff, #6666ff);
}

.bar-text {
    font-size: 12px;
    min-width: 50px;
    text-align: right;
}

/* Chat Panel */
#chat-panel {
    bottom: 10px;
    left: 10px;
    width: 400px;
    height: 200px;
    display: flex;
    flex-direction: column;
}

#chat-messages {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.4;
}

.chat-message {
    margin-bottom: 5px;
    padding: 2px 5px;
    border-radius: 3px;
}

.chat-message.system {
    color: #FFD700;
    font-style: italic;
}

.chat-message.player {
    color: #4CAF50;
}

#chat-input-container {
    display: flex;
}

#chat-input {
    flex: 1;
    padding: 8px;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

#chat-send {
    margin-left: 5px;
    padding: 8px 15px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* Players Panel */
#players-panel {
    top: 10px;
    right: 10px;
    width: 200px;
    max-height: 300px;
}

#players-panel h3 {
    margin-bottom: 10px;
    color: #4CAF50;
    font-size: 16px;
}

#players-list {
    font-size: 14px;
}

.player-item {
    padding: 5px;
    margin-bottom: 3px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.player-item.self {
    background: rgba(76, 175, 80, 0.3);
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
