const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const cors = require('cors');
require('dotenv').config();

const GameServer = require('./game/GameServer');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../client')));

// Serve the main game page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/index.html'));
});

// API Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/game', require('./routes/api'));

// Initialize Game Server
const gameServer = new GameServer(io);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`Player connected: ${socket.id}`);
  
  // Handle player joining the game
  socket.on('join-game', (playerData) => {
    gameServer.addPlayer(socket, playerData);
  });
  
  // Handle player movement
  socket.on('player-move', (moveData) => {
    gameServer.handlePlayerMove(socket.id, moveData);
  });
  
  // Handle player chat
  socket.on('chat-message', (message) => {
    gameServer.handleChatMessage(socket.id, message);
  });
  
  // Handle player disconnect
  socket.on('disconnect', () => {
    console.log(`Player disconnected: ${socket.id}`);
    gameServer.removePlayer(socket.id);
  });
});

server.listen(PORT, () => {
  console.log(`🎮 Node RPG Server running on port ${PORT}`);
  console.log(`🌐 Game available at http://localhost:${PORT}`);
});
