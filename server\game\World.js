class World {
  constructor() {
    this.width = 1600;
    this.height = 1200;
    this.name = "Starter Town";
    
    // Define world boundaries and obstacles
    this.obstacles = [
      // Example obstacles (buildings, trees, etc.)
      { x: 200, y: 200, width: 100, height: 100, type: 'building' },
      { x: 500, y: 150, width: 80, height: 120, type: 'tree' },
      { x: 800, y: 300, width: 150, height: 100, type: 'building' }
    ];
    
    // NPCs in the world
    this.npcs = [
      {
        id: 'npc_1',
        name: 'Village Elder',
        x: 300,
        y: 400,
        sprite: 'elder_npc',
        dialogue: ['Welcome to our village!', 'Be careful out there, adventurer.']
      },
      {
        id: 'npc_2',
        name: 'Merchant',
        x: 600,
        y: 500,
        sprite: 'merchant_npc',
        dialogue: ['Looking to buy some gear?', 'I have the finest weapons and armor!']
      }
    ];
    
    // Spawn points for new players
    this.spawnPoints = [
      { x: 400, y: 300 },
      { x: 450, y: 300 },
      { x: 400, y: 350 },
      { x: 450, y: 350 }
    ];
    
    console.log(`World "${this.name}" initialized (${this.width}x${this.height})`);
  }
  
  isValidPosition(x, y) {
    // Check if position is within world bounds
    if (x < 0 || x > this.width || y < 0 || y > this.height) {
      return false;
    }
    
    // Check collision with obstacles
    for (const obstacle of this.obstacles) {
      if (x >= obstacle.x && 
          x <= obstacle.x + obstacle.width &&
          y >= obstacle.y && 
          y <= obstacle.y + obstacle.height) {
        return false;
      }
    }
    
    return true;
  }
  
  getRandomSpawnPoint() {
    const randomIndex = Math.floor(Math.random() * this.spawnPoints.length);
    return this.spawnPoints[randomIndex];
  }
  
  getNearbyNPCs(x, y, radius = 100) {
    return this.npcs.filter(npc => {
      const distance = Math.sqrt(
        Math.pow(npc.x - x, 2) + Math.pow(npc.y - y, 2)
      );
      return distance <= radius;
    });
  }
  
  update() {
    // Update world state (day/night cycle, weather, etc.)
    // For now, this is just a placeholder
  }
  
  getState() {
    return {
      name: this.name,
      width: this.width,
      height: this.height,
      obstacles: this.obstacles,
      npcs: this.npcs,
      spawnPoints: this.spawnPoints
    };
  }
}

module.exports = World;
