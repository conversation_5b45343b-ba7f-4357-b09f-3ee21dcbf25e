{"name": "node-rpg", "version": "1.0.0", "description": "Adventure Quest Worlds-style MMORPG using Express.js, Socket.IO, and Phaser", "main": "server/index.js", "scripts": {"start": "node server/index.js", "dev": "nodemon server/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mmorpg", "game", "multiplayer", "phaser", "socket.io"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}