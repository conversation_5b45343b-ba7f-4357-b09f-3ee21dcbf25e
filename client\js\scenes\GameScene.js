class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        this.player = null;
        this.otherPlayersGroup = null;
        this.npcsGroup = null;
        this.cursors = null;
        this.wasd = null;
        this.lastMoveTime = 0;
        this.moveDelay = 100; // Minimum time between moves in ms
    }
    
    create() {
        // Create world background
        this.createWorld();
        
        // Create player
        this.createPlayer();
        
        // Create groups for other entities
        this.otherPlayersGroup = this.add.group();
        this.npcsGroup = this.add.group();
        
        // Set up camera
        this.cameras.main.startFollow(this.player);
        this.cameras.main.setBounds(0, 0, 1600, 1200);
        
        // Set up input
        this.cursors = this.input.keyboard.createCursorKeys();
        this.wasd = this.input.keyboard.addKeys('W,S,A,D');
        
        console.log('Game scene created');
    }
    
    createWorld() {
        // Create a tiled background
        for (let x = 0; x < 1600; x += 32) {
            for (let y = 0; y < 1200; y += 32) {
                this.add.image(x, y, 'ground_tile').setOrigin(0);
            }
        }
        
        // Add some decorative elements
        this.addWorldObjects();
    }
    
    addWorldObjects() {
        // Add trees
        const treePositions = [
            { x: 200, y: 200 }, { x: 500, y: 150 }, { x: 800, y: 300 },
            { x: 1200, y: 400 }, { x: 300, y: 600 }, { x: 1000, y: 800 }
        ];
        
        treePositions.forEach(pos => {
            this.add.image(pos.x, pos.y, 'tree_sprite');
        });
        
        // Add buildings
        const buildingPositions = [
            { x: 400, y: 300 }, { x: 900, y: 500 }, { x: 1300, y: 200 }
        ];
        
        buildingPositions.forEach(pos => {
            this.add.image(pos.x, pos.y, 'building_sprite');
        });
        
        // Add NPCs
        this.addNPC(300, 400, 'Village Elder');
        this.addNPC(600, 500, 'Merchant');
    }
    
    addNPC(x, y, name) {
        const npc = this.add.image(x, y, 'npc_sprite');
        npc.setInteractive();
        npc.npcName = name;
        
        npc.on('pointerdown', () => {
            this.interactWithNPC(npc);
        });
        
        this.npcsGroup.add(npc);
    }
    
    interactWithNPC(npc) {
        addChatMessage(`${npc.npcName}: Hello, adventurer!`, 'system');
    }
    
    createPlayer() {
        // Create player sprite at starting position
        this.player = this.add.image(400, 300, 'player_sprite');
        this.player.setInteractive();
        
        // Store player data
        this.player.playerData = {
            x: 400,
            y: 300,
            direction: 'down'
        };
    }
    
    update() {
        if (!this.player) return;
        
        this.handlePlayerMovement();
    }
    
    handlePlayerMovement() {
        const currentTime = Date.now();
        if (currentTime - this.lastMoveTime < this.moveDelay) {
            return;
        }
        
        let moveX = 0;
        let moveY = 0;
        let direction = this.player.playerData.direction;
        
        // Check input
        if (this.cursors.left.isDown || this.wasd.A.isDown) {
            moveX = -32;
            direction = 'left';
        } else if (this.cursors.right.isDown || this.wasd.D.isDown) {
            moveX = 32;
            direction = 'right';
        }
        
        if (this.cursors.up.isDown || this.wasd.W.isDown) {
            moveY = -32;
            direction = 'up';
        } else if (this.cursors.down.isDown || this.wasd.S.isDown) {
            moveY = 32;
            direction = 'down';
        }
        
        // If there's movement, update player position
        if (moveX !== 0 || moveY !== 0) {
            const newX = this.player.x + moveX;
            const newY = this.player.y + moveY;
            
            // Check bounds
            if (newX >= 16 && newX <= 1584 && newY >= 16 && newY <= 1184) {
                this.player.x = newX;
                this.player.y = newY;
                this.player.playerData.x = newX;
                this.player.playerData.y = newY;
                this.player.playerData.direction = direction;
                
                // Send movement to server
                if (socket) {
                    socket.emit('player-move', {
                        x: newX,
                        y: newY,
                        direction: direction
                    });
                }
                
                this.lastMoveTime = currentTime;
            }
        }
    }
    
    addOtherPlayer(playerData) {
        const otherPlayer = this.add.image(playerData.x, playerData.y, 'player_sprite_0');
        otherPlayer.playerId = playerData.id;
        otherPlayer.playerName = playerData.name;
        
        // Add name label
        const nameText = this.add.text(playerData.x, playerData.y - 20, playerData.name, {
            fontSize: '12px',
            fill: '#ffffff',
            backgroundColor: '#000000',
            padding: { x: 4, y: 2 }
        }).setOrigin(0.5);
        
        otherPlayer.nameText = nameText;
        this.otherPlayersGroup.add(otherPlayer);
        
        console.log(`Added other player: ${playerData.name}`);
    }
    
    removeOtherPlayer(playerId) {
        this.otherPlayersGroup.children.entries.forEach(player => {
            if (player.playerId === playerId) {
                if (player.nameText) {
                    player.nameText.destroy();
                }
                player.destroy();
            }
        });
    }
    
    updateOtherPlayerPosition(moveData) {
        this.otherPlayersGroup.children.entries.forEach(player => {
            if (player.playerId === moveData.playerId) {
                // Smooth movement
                this.tweens.add({
                    targets: player,
                    x: moveData.x,
                    y: moveData.y,
                    duration: 100,
                    ease: 'Linear'
                });
                
                // Update name text position
                if (player.nameText) {
                    this.tweens.add({
                        targets: player.nameText,
                        x: moveData.x,
                        y: moveData.y - 20,
                        duration: 100,
                        ease: 'Linear'
                    });
                }
            }
        });
    }
}
