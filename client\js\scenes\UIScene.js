class UIScene extends Phaser.Scene {
    constructor() {
        super({ key: 'UIScene' });
    }
    
    create() {
        // This scene handles UI overlays that appear on top of the game
        // Most UI is handled by HTML/CSS, but we can add Phaser UI elements here
        
        // Add debug info (can be removed later)
        this.debugText = this.add.text(10, 10, '', {
            fontSize: '14px',
            fill: '#ffffff',
            backgroundColor: '#000000',
            padding: { x: 5, y: 5 }
        });
        
        // Update debug info periodically
        this.time.addEvent({
            delay: 1000,
            callback: this.updateDebugInfo,
            callbackScope: this,
            loop: true
        });
    }
    
    updateDebugInfo() {
        if (this.debugText && game) {
            const gameScene = game.scene.getScene('GameScene');
            let debugInfo = 'Node RPG Debug\n';
            debugInfo += `FPS: ${Math.round(game.loop.actualFps)}\n`;
            
            if (gameScene && gameScene.player) {
                debugInfo += `Player: (${gameScene.player.x}, ${gameScene.player.y})\n`;
            }
            
            debugInfo += `Connected: ${socket && socket.connected ? 'Yes' : 'No'}\n`;
            debugInfo += `Other Players: ${otherPlayers.size}`;
            
            this.debugText.setText(debugInfo);
        }
    }
    
    showNotification(message, duration = 3000) {
        const notification = this.add.text(512, 100, message, {
            fontSize: '18px',
            fill: '#ffffff',
            backgroundColor: '#4CAF50',
            padding: { x: 20, y: 10 }
        }).setOrigin(0.5);
        
        // Fade in
        notification.setAlpha(0);
        this.tweens.add({
            targets: notification,
            alpha: 1,
            duration: 300,
            ease: 'Power2'
        });
        
        // Fade out and destroy after duration
        this.time.delayedCall(duration, () => {
            this.tweens.add({
                targets: notification,
                alpha: 0,
                duration: 300,
                ease: 'Power2',
                onComplete: () => {
                    notification.destroy();
                }
            });
        });
    }
    
    showDamageNumber(x, y, damage, color = '#ff0000') {
        const damageText = this.add.text(x, y, `-${damage}`, {
            fontSize: '16px',
            fill: color,
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Animate damage number
        this.tweens.add({
            targets: damageText,
            y: y - 50,
            alpha: 0,
            duration: 1000,
            ease: 'Power2',
            onComplete: () => {
                damageText.destroy();
            }
        });
    }
    
    showLevelUp(playerName) {
        const levelUpText = this.add.text(512, 200, `${playerName} Level Up!`, {
            fontSize: '32px',
            fill: '#FFD700',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);
        
        // Scale animation
        levelUpText.setScale(0);
        this.tweens.add({
            targets: levelUpText,
            scaleX: 1,
            scaleY: 1,
            duration: 500,
            ease: 'Back.easeOut'
        });
        
        // Remove after 3 seconds
        this.time.delayedCall(3000, () => {
            this.tweens.add({
                targets: levelUpText,
                alpha: 0,
                duration: 500,
                onComplete: () => {
                    levelUpText.destroy();
                }
            });
        });
    }
}
