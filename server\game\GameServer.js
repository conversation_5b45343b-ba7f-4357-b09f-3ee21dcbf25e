const Player = require('./Player');
const World = require('./World');

class GameServer {
  constructor(io) {
    this.io = io;
    this.players = new Map();
    this.world = new World();
    
    // Game loop - update every 16ms (60 FPS)
    this.gameLoop = setInterval(() => {
      this.update();
    }, 16);
    
    console.log('🎮 Game Server initialized');
  }
  
  addPlayer(socket, playerData) {
    const player = new Player(socket.id, playerData);
    this.players.set(socket.id, player);
    
    // Send current world state to new player
    socket.emit('world-state', this.world.getState());
    
    // Send current players to new player
    const playersData = Array.from(this.players.values()).map(p => p.getPublicData());
    socket.emit('players-update', playersData);
    
    // Notify other players about new player
    socket.broadcast.emit('player-joined', player.getPublicData());
    
    console.log(`Player ${playerData.name || socket.id} joined the game`);
  }
  
  removePlayer(socketId) {
    const player = this.players.get(socketId);
    if (player) {
      this.players.delete(socketId);
      
      // Notify other players about player leaving
      this.io.emit('player-left', socketId);
      
      console.log(`Player ${player.name || socketId} left the game`);
    }
  }
  
  handlePlayerMove(socketId, moveData) {
    const player = this.players.get(socketId);
    if (player) {
      // Validate and update player position
      if (this.world.isValidPosition(moveData.x, moveData.y)) {
        player.updatePosition(moveData.x, moveData.y, moveData.direction);
        
        // Broadcast movement to other players
        this.io.emit('player-moved', {
          playerId: socketId,
          x: moveData.x,
          y: moveData.y,
          direction: moveData.direction
        });
      }
    }
  }
  
  handleChatMessage(socketId, message) {
    const player = this.players.get(socketId);
    if (player && message.trim().length > 0) {
      const chatData = {
        playerId: socketId,
        playerName: player.name,
        message: message.trim(),
        timestamp: Date.now()
      };
      
      // Broadcast chat message to all players
      this.io.emit('chat-message', chatData);
    }
  }
  
  update() {
    // Game logic updates (combat, NPCs, etc.)
    // This runs 60 times per second
    
    // Update world state
    this.world.update();
    
    // Update all players
    for (const player of this.players.values()) {
      player.update();
    }
    
    // Send periodic updates to clients if needed
    // (For now, we'll only send updates when things change)
  }
  
  getPlayerCount() {
    return this.players.size;
  }
}

module.exports = GameServer;
