const express = require('express');
const router = express.Router();

// For now, we'll implement simple guest login
// Later you can add proper authentication with bcrypt and JWT

// Guest login - creates a temporary player session
router.post('/guest-login', (req, res) => {
  const { playerName } = req.body;
  
  if (!playerName || playerName.trim().length === 0) {
    return res.status(400).json({ error: 'Player name is required' });
  }
  
  // Create a simple guest session
  const guestPlayer = {
    id: `guest_${Date.now()}`,
    name: playerName.trim(),
    isGuest: true,
    createdAt: new Date()
  };
  
  res.json({
    success: true,
    player: guestPlayer,
    message: 'Guest login successful'
  });
});

// Register new account (placeholder for future implementation)
router.post('/register', (req, res) => {
  res.status(501).json({ 
    error: 'Registration not implemented yet',
    message: 'Use guest login for now'
  });
});

// Login with account (placeholder for future implementation)
router.post('/login', (req, res) => {
  res.status(501).json({ 
    error: 'Account login not implemented yet',
    message: 'Use guest login for now'
  });
});

module.exports = router;
