// Main application entry point
// This file handles the overall application flow and coordination

// Global application state
const AppState = {
    isGameStarted: false,
    currentScene: 'login',
    serverConnected: false,
    playerLoggedIn: false
};

// Application initialization
document.addEventListener('DOMContentLoaded', () => {
    console.log('Node RPG Client Starting...');
    
    // Initialize the application
    initializeApp();
});

function initializeApp() {
    // Set up global error handling
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    // Initialize UI event listeners
    setupUIEventListeners();
    
    // Check server status
    checkServerStatus();
    
    console.log('Application initialized');
}

function setupUIEventListeners() {
    // Keyboard shortcuts
    document.addEventListener('keydown', handleGlobalKeydown);
    
    // Window resize handling
    window.addEventListener('resize', handleWindowResize);
    
    // Visibility change (tab switching)
    document.addEventListener('visibilitychange', handleVisibilityChange);
}

function handleGlobalKeydown(event) {
    // Global keyboard shortcuts
    switch(event.key) {
        case 'Escape':
            // Toggle game menu or close dialogs
            toggleGameMenu();
            break;
        case 'Enter':
            // Focus chat input if in game
            if (AppState.isGameStarted && !event.target.matches('input')) {
                const chatInput = document.getElementById('chat-input');
                if (chatInput) {
                    chatInput.focus();
                    event.preventDefault();
                }
            }
            break;
        case 'F11':
            // Toggle fullscreen
            toggleFullscreen();
            event.preventDefault();
            break;
    }
}

function handleWindowResize() {
    // Resize Phaser game if it exists
    if (game && game.canvas) {
        const canvas = game.canvas;
        const container = document.getElementById('phaser-game');
        if (container) {
            // Maintain aspect ratio
            const containerRect = container.getBoundingClientRect();
            const scale = Math.min(
                containerRect.width / 1024,
                containerRect.height / 768
            );
            
            canvas.style.width = (1024 * scale) + 'px';
            canvas.style.height = (768 * scale) + 'px';
        }
    }
}

function handleVisibilityChange() {
    if (document.hidden) {
        // Game tab is not visible
        console.log('Game tab hidden');
        // You might want to pause certain game activities
    } else {
        // Game tab is visible again
        console.log('Game tab visible');
        // Resume game activities
    }
}

function toggleGameMenu() {
    // Implement game menu toggle
    console.log('Game menu toggle (not implemented yet)');
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.log('Error attempting to enable fullscreen:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

function checkServerStatus() {
    fetch('/api/game/status')
        .then(response => response.json())
        .then(data => {
            console.log('Server status:', data);
            AppState.serverConnected = true;
            updateConnectionStatus(true);
        })
        .catch(error => {
            console.error('Server connection failed:', error);
            AppState.serverConnected = false;
            updateConnectionStatus(false);
        });
}

function updateConnectionStatus(connected) {
    const statusElement = document.getElementById('login-status');
    if (statusElement) {
        if (connected) {
            statusElement.textContent = 'Server connected';
            statusElement.style.color = '#4CAF50';
        } else {
            statusElement.textContent = 'Server connection failed';
            statusElement.style.color = '#f44336';
        }
    }
}

function handleGlobalError(event) {
    console.error('Global error:', event.error);
    // You might want to show an error message to the user
    showErrorMessage('An unexpected error occurred. Please refresh the page.');
}

function handleUnhandledRejection(event) {
    console.error('Unhandled promise rejection:', event.reason);
    // Handle promise rejections
}

function showErrorMessage(message) {
    // Create a simple error notification
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f44336;
        color: white;
        padding: 15px;
        border-radius: 5px;
        z-index: 10000;
        max-width: 300px;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    // Remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

// Utility functions
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
}

function sanitizeInput(input) {
    // Basic input sanitization
    return input.replace(/[<>]/g, '');
}

// Export functions that might be needed globally
window.AppState = AppState;
window.showErrorMessage = showErrorMessage;
window.formatTime = formatTime;
window.sanitizeInput = sanitizeInput;

console.log('Main.js loaded');
