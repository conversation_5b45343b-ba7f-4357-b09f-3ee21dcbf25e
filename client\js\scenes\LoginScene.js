class LoginScene extends Phaser.Scene {
    constructor() {
        super({ key: 'LoginScene' });
    }
    
    preload() {
        // Create simple colored rectangles as placeholders for sprites
        this.load.image('logo', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    }
    
    create() {
        // This scene is mainly for preloading assets
        // The actual login UI is handled by HTML/CSS
        
        // Add background
        this.add.rectangle(512, 384, 1024, 768, 0x2c5530);
        
        // Add some decorative elements
        this.add.text(512, 200, 'Node RPG', {
            fontSize: '48px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        
        this.add.text(512, 260, 'Loading...', {
            fontSize: '24px',
            fill: '#cccccc',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        
        // Preload game assets here
        this.preloadGameAssets();
    }
    
    preloadGameAssets() {
        // Create placeholder sprites using graphics
        this.createPlayerSprites();
        this.createWorldSprites();
        
        console.log('Login scene assets loaded');
    }
    
    createPlayerSprites() {
        // Create a simple player sprite using graphics
        const graphics = this.add.graphics();
        graphics.fillStyle(0x4A90E2);
        graphics.fillRect(0, 0, 32, 32);
        graphics.generateTexture('player_sprite', 32, 32);
        graphics.destroy();
        
        // Create different colored player sprites for other players
        const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3];
        colors.forEach((color, index) => {
            const g = this.add.graphics();
            g.fillStyle(color);
            g.fillRect(0, 0, 32, 32);
            g.generateTexture(`player_sprite_${index}`, 32, 32);
            g.destroy();
        });
    }
    
    createWorldSprites() {
        // Create ground tile
        const ground = this.add.graphics();
        ground.fillStyle(0x4CAF50);
        ground.fillRect(0, 0, 32, 32);
        ground.generateTexture('ground_tile', 32, 32);
        ground.destroy();
        
        // Create tree sprite
        const tree = this.add.graphics();
        tree.fillStyle(0x8B4513);
        tree.fillRect(12, 20, 8, 12);
        tree.fillStyle(0x228B22);
        tree.fillCircle(16, 16, 12);
        tree.generateTexture('tree_sprite', 32, 32);
        tree.destroy();
        
        // Create building sprite
        const building = this.add.graphics();
        building.fillStyle(0x8B4513);
        building.fillRect(0, 8, 32, 24);
        building.fillStyle(0xFF0000);
        building.fillTriangle(16, 0, 0, 8, 32, 8);
        building.generateTexture('building_sprite', 32, 32);
        building.destroy();
        
        // Create NPC sprite
        const npc = this.add.graphics();
        npc.fillStyle(0xFFD700);
        npc.fillRect(0, 0, 32, 32);
        npc.fillStyle(0x000000);
        npc.fillRect(8, 8, 4, 4);
        npc.fillRect(20, 8, 4, 4);
        npc.fillRect(12, 20, 8, 4);
        npc.generateTexture('npc_sprite', 32, 32);
        npc.destroy();
    }
}
