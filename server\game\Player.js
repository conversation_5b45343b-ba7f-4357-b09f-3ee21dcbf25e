class Player {
  constructor(socketId, playerData) {
    this.id = socketId;
    this.name = playerData.name || `Player_${socketId.substring(0, 6)}`;
    this.x = playerData.x || 400; // Starting position
    this.y = playerData.y || 300;
    this.direction = 'down';
    
    // Player stats
    this.level = playerData.level || 1;
    this.health = playerData.health || 100;
    this.maxHealth = 100;
    this.mana = playerData.mana || 50;
    this.maxMana = 50;
    this.experience = playerData.experience || 0;
    
    // Player appearance
    this.sprite = playerData.sprite || 'default_player';
    this.color = playerData.color || '#4A90E2';
    
    // Player state
    this.isMoving = false;
    this.lastMoveTime = Date.now();
    this.isInCombat = false;
    
    console.log(`Player created: ${this.name} at (${this.x}, ${this.y})`);
  }
  
  updatePosition(x, y, direction) {
    this.x = x;
    this.y = y;
    this.direction = direction || this.direction;
    this.lastMoveTime = Date.now();
  }
  
  takeDamage(amount) {
    this.health = Math.max(0, this.health - amount);
    return this.health <= 0; // Returns true if player died
  }
  
  heal(amount) {
    this.health = Math.min(this.maxHealth, this.health + amount);
  }
  
  gainExperience(amount) {
    this.experience += amount;
    
    // Simple level up calculation
    const expNeeded = this.level * 100;
    if (this.experience >= expNeeded) {
      this.levelUp();
    }
  }
  
  levelUp() {
    this.level++;
    this.experience = 0;
    this.maxHealth += 20;
    this.maxMana += 10;
    this.health = this.maxHealth; // Full heal on level up
    this.mana = this.maxMana;
    
    console.log(`${this.name} leveled up to level ${this.level}!`);
  }
  
  update() {
    // Update player state each frame
    // Regenerate mana slowly
    if (this.mana < this.maxMana) {
      this.mana = Math.min(this.maxMana, this.mana + 0.1);
    }
    
    // Check if player is still moving
    if (Date.now() - this.lastMoveTime > 100) {
      this.isMoving = false;
    }
  }
  
  getPublicData() {
    return {
      id: this.id,
      name: this.name,
      x: this.x,
      y: this.y,
      direction: this.direction,
      level: this.level,
      health: this.health,
      maxHealth: this.maxHealth,
      sprite: this.sprite,
      color: this.color,
      isMoving: this.isMoving,
      isInCombat: this.isInCombat
    };
  }
  
  getPrivateData() {
    return {
      ...this.getPublicData(),
      mana: this.mana,
      maxMana: this.maxMana,
      experience: this.experience
    };
  }
}

module.exports = Player;
