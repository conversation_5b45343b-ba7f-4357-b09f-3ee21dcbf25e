// Player entity class for client-side player representation
class ClientPlayer {
    constructor(scene, x, y, playerData) {
        this.scene = scene;
        this.playerData = playerData;
        
        // Create sprite
        this.sprite = scene.add.image(x, y, 'player_sprite');
        this.sprite.setInteractive();
        
        // Player properties
        this.x = x;
        this.y = y;
        this.direction = 'down';
        this.isMoving = false;
        this.moveSpeed = 32; // Grid-based movement
        
        // Animation and visual effects
        this.nameText = scene.add.text(x, y - 25, playerData.name, {
            fontSize: '12px',
            fill: '#ffffff',
            backgroundColor: '#000000',
            padding: { x: 4, y: 2 }
        }).setOrigin(0.5);
        
        // Health bar (optional, for other players)
        if (playerData.showHealthBar) {
            this.createHealthBar();
        }
    }
    
    createHealthBar() {
        const barWidth = 32;
        const barHeight = 4;
        
        // Background
        this.healthBarBg = this.scene.add.rectangle(
            this.x, this.y - 35, barWidth, barHeight, 0x000000
        );
        
        // Health fill
        this.healthBarFill = this.scene.add.rectangle(
            this.x, this.y - 35, barWidth, barHeight, 0xff0000
        );
        
        this.updateHealthBar(this.playerData.health, this.playerData.maxHealth);
    }
    
    updateHealthBar(health, maxHealth) {
        if (this.healthBarFill) {
            const healthPercent = health / maxHealth;
            this.healthBarFill.scaleX = healthPercent;
            
            // Change color based on health
            if (healthPercent > 0.6) {
                this.healthBarFill.setFillStyle(0x00ff00); // Green
            } else if (healthPercent > 0.3) {
                this.healthBarFill.setFillStyle(0xffff00); // Yellow
            } else {
                this.healthBarFill.setFillStyle(0xff0000); // Red
            }
        }
    }
    
    moveTo(x, y, direction) {
        this.isMoving = true;
        this.direction = direction;
        
        // Smooth movement animation
        this.scene.tweens.add({
            targets: this.sprite,
            x: x,
            y: y,
            duration: 150,
            ease: 'Linear',
            onComplete: () => {
                this.isMoving = false;
                this.x = x;
                this.y = y;
            }
        });
        
        // Move name text
        this.scene.tweens.add({
            targets: this.nameText,
            x: x,
            y: y - 25,
            duration: 150,
            ease: 'Linear'
        });
        
        // Move health bar if it exists
        if (this.healthBarBg && this.healthBarFill) {
            this.scene.tweens.add({
                targets: [this.healthBarBg, this.healthBarFill],
                x: x,
                y: y - 35,
                duration: 150,
                ease: 'Linear'
            });
        }
    }
    
    playAttackAnimation() {
        // Simple attack animation - scale up and down
        this.scene.tweens.add({
            targets: this.sprite,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 100,
            yoyo: true,
            ease: 'Power2'
        });
    }
    
    playHitAnimation() {
        // Flash red when taking damage
        this.sprite.setTint(0xff0000);
        this.scene.time.delayedCall(200, () => {
            this.sprite.clearTint();
        });
    }
    
    setDirection(direction) {
        this.direction = direction;
        // In a more advanced implementation, you would change the sprite frame
        // based on direction for walking animations
    }
    
    update() {
        // Update player state each frame
        // This could include animation updates, status effects, etc.
    }
    
    destroy() {
        if (this.sprite) this.sprite.destroy();
        if (this.nameText) this.nameText.destroy();
        if (this.healthBarBg) this.healthBarBg.destroy();
        if (this.healthBarFill) this.healthBarFill.destroy();
    }
    
    // Getters for position
    getX() { return this.x; }
    getY() { return this.y; }
    getDirection() { return this.direction; }
    isPlayerMoving() { return this.isMoving; }
}
