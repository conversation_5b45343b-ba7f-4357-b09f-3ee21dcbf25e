// Global game variables
let socket;
let game;
let currentPlayer = null;
let otherPlayers = new Map();

// Game configuration
const gameConfig = {
    type: Phaser.AUTO,
    width: 1024,
    height: 768,
    parent: 'phaser-game',
    backgroundColor: '#2c5530',
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 0 },
            debug: false
        }
    },
    scene: [LoginScene, GameScene, UIScene]
};

// Socket.IO connection
function connectToServer() {
    socket = io();
    
    socket.on('connect', () => {
        console.log('Connected to server');
        addChatMessage('Connected to server', 'system');
    });
    
    socket.on('disconnect', () => {
        console.log('Disconnected from server');
        addChatMessage('Disconnected from server', 'system');
    });
    
    socket.on('world-state', (worldData) => {
        console.log('Received world state:', worldData);
        // Handle world initialization
    });
    
    socket.on('players-update', (playersData) => {
        console.log('Players update:', playersData);
        updatePlayersList(playersData);
    });
    
    socket.on('player-joined', (playerData) => {
        console.log('Player joined:', playerData);
        addChatMessage(`${playerData.name} joined the game`, 'system');
        addOtherPlayer(playerData);
    });
    
    socket.on('player-left', (playerId) => {
        console.log('Player left:', playerId);
        removeOtherPlayer(playerId);
    });
    
    socket.on('player-moved', (moveData) => {
        updateOtherPlayerPosition(moveData);
    });
    
    socket.on('chat-message', (chatData) => {
        addChatMessage(`${chatData.playerName}: ${chatData.message}`, 'player');
    });
}

// UI Functions
function showScreen(screenId) {
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById(screenId).classList.add('active');
}

function updatePlayerInfo(playerData) {
    document.querySelector('.player-name').textContent = playerData.name;
    document.querySelector('.player-level').textContent = `Level ${playerData.level}`;
    
    const healthPercent = (playerData.health / playerData.maxHealth) * 100;
    const manaPercent = (playerData.mana / playerData.maxMana) * 100;
    
    document.querySelector('.health-fill').style.width = `${healthPercent}%`;
    document.querySelector('.mana-fill').style.width = `${manaPercent}%`;
    
    document.querySelector('.health-bar .bar-text').textContent = `${playerData.health}/${playerData.maxHealth}`;
    document.querySelector('.mana-bar .bar-text').textContent = `${playerData.mana}/${playerData.maxMana}`;
}

function addChatMessage(message, type = 'player') {
    const chatMessages = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-message ${type}`;
    messageDiv.textContent = message;
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Limit chat history to 100 messages
    while (chatMessages.children.length > 100) {
        chatMessages.removeChild(chatMessages.firstChild);
    }
}

function updatePlayersList(playersData) {
    const playersList = document.getElementById('players-list');
    playersList.innerHTML = '';
    
    playersData.forEach(player => {
        const playerDiv = document.createElement('div');
        playerDiv.className = 'player-item';
        if (currentPlayer && player.id === currentPlayer.id) {
            playerDiv.classList.add('self');
        }
        playerDiv.textContent = `${player.name} (Lv.${player.level})`;
        playersList.appendChild(playerDiv);
    });
}

function addOtherPlayer(playerData) {
    otherPlayers.set(playerData.id, playerData);
    // Update the game scene if it's active
    if (game && game.scene.isActive('GameScene')) {
        const gameScene = game.scene.getScene('GameScene');
        gameScene.addOtherPlayer(playerData);
    }
}

function removeOtherPlayer(playerId) {
    const player = otherPlayers.get(playerId);
    if (player) {
        addChatMessage(`${player.name} left the game`, 'system');
        otherPlayers.delete(playerId);
        
        // Remove from game scene if active
        if (game && game.scene.isActive('GameScene')) {
            const gameScene = game.scene.getScene('GameScene');
            gameScene.removeOtherPlayer(playerId);
        }
    }
}

function updateOtherPlayerPosition(moveData) {
    const player = otherPlayers.get(moveData.playerId);
    if (player) {
        player.x = moveData.x;
        player.y = moveData.y;
        player.direction = moveData.direction;
        
        // Update in game scene if active
        if (game && game.scene.isActive('GameScene')) {
            const gameScene = game.scene.getScene('GameScene');
            gameScene.updateOtherPlayerPosition(moveData);
        }
    }
}

// Initialize the application
function init() {
    connectToServer();
    setupEventListeners();
}

function setupEventListeners() {
    // Login form
    document.getElementById('login-form').addEventListener('submit', handleLogin);
    
    // Chat
    document.getElementById('chat-input').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendChatMessage();
        }
    });
    
    document.getElementById('chat-send').addEventListener('click', sendChatMessage);
}

function handleLogin(e) {
    e.preventDefault();
    const playerName = document.getElementById('player-name').value.trim();
    
    if (!playerName) {
        document.getElementById('login-status').textContent = 'Please enter a name';
        return;
    }
    
    // Send guest login request
    fetch('/api/auth/guest-login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ playerName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentPlayer = data.player;
            currentPlayer.name = playerName;
            startGame();
        } else {
            document.getElementById('login-status').textContent = data.error || 'Login failed';
        }
    })
    .catch(error => {
        console.error('Login error:', error);
        document.getElementById('login-status').textContent = 'Connection error';
    });
}

function sendChatMessage() {
    const chatInput = document.getElementById('chat-input');
    const message = chatInput.value.trim();
    
    if (message && socket) {
        socket.emit('chat-message', message);
        chatInput.value = '';
    }
}

function startGame() {
    showScreen('game-screen');
    
    // Initialize Phaser game
    game = new Phaser.Game(gameConfig);
    
    // Update player info UI
    updatePlayerInfo({
        name: currentPlayer.name,
        level: 1,
        health: 100,
        maxHealth: 100,
        mana: 50,
        maxMana: 50
    });
    
    // Join the game on the server
    socket.emit('join-game', currentPlayer);
    
    addChatMessage(`Welcome to Node RPG, ${currentPlayer.name}!`, 'system');
}

// Start the application when page loads
document.addEventListener('DOMContentLoaded', init);
